// 独立的表格分页样式优化，不影响其他组件
.customTablePagination1 {
  :global {
    // 分页容器样式
    .ant-pagination {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 16px;
      padding: 12px 0;
      background: #fafafa;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      // 分页项通用样式
      .ant-pagination-item,
      .ant-pagination-prev,
      .ant-pagination-next {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        margin: 0 4px;
        min-width: 32px;
        height: 32px;
        line-height: 30px;
        text-align: center;
        transition: all 0.3s ease;
        background: #fff;

        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(64, 169, 255, 0.2);
        }

        a {
          display: block;
          color: inherit;
          text-decoration: none;
        }
      }

      // 当前页样式
      .ant-pagination-item-active {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);

        a {
          color: #fff;
          font-weight: 500;
        }

        &:hover {
          background: linear-gradient(135deg, #40a9ff, #69c0ff);
          border-color: #40a9ff;
          transform: translateY(-1px);
        }
      }

      // 禁用状态
      .ant-pagination-disabled {
        background: #f5f5f5;

        &:hover {
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.25);
          transform: none;
          box-shadow: none;
        }
      }
      
      // 页码选择器样式
      .ant-pagination-options {
        margin-left: 16px;
        display: flex;
        align-items: center;

        .ant-pagination-options-size-changer {
          margin-right: 16px;

          .ant-select {
            .ant-select-selector {
              border-radius: 6px;
              height: 32px;
              line-height: 30px;
              border: 1px solid #d9d9d9;
              transition: all 0.3s ease;

              &:hover {
                border-color: #40a9ff;
                box-shadow: 0 2px 4px rgba(64, 169, 255, 0.2);
              }
            }

            &.ant-select-focused .ant-select-selector {
              border-color: #40a9ff;
              box-shadow: 0 0 0 2px rgba(64, 169, 255, 0.2);
            }
          }
        }

        .ant-pagination-options-quick-jumper {
          input {
            border-radius: 6px;
            height: 32px;
            width: 60px;
            text-align: center;
            border: 1px solid #d9d9d9;
            transition: all 0.3s ease;

            &:hover {
              border-color: #40a9ff;
            }

            &:focus {
              border-color: #40a9ff;
              box-shadow: 0 0 0 2px rgba(64, 169, 255, 0.2);
            }
          }
        }
      }

      // 总数显示
      .ant-pagination-total-text {
        margin-right: 16px;
        color: #666;
        font-size: 14px;
        font-weight: 500;
        background: #fff;
        padding: 4px 12px;
        border-radius: 16px;
        border: 1px solid #e8e8e8;
      }

      // 跳转文字样式
      .ant-pagination-options-quick-jumper {
        color: #666;
        font-size: 14px;

        input {
          margin: 0 8px;
        }
      }

      // 每页条数选择器
      .ant-pagination-options-size-changer {
        .ant-select {
          margin-right: 8px;
        }
      }

      // 省略号样式优化
      .ant-pagination-jump-prev,
      .ant-pagination-jump-next {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        margin: 0 4px;
        min-width: 32px;
        height: 32px;
        line-height: 30px;
        text-align: center;
        transition: all 0.3s ease;
        background: #fff;

        .ant-pagination-item-container {
          .ant-pagination-item-link-icon {
            color: #1890ff;
            font-size: 12px;
          }

          .ant-pagination-item-ellipsis {
            color: #999;
            font-size: 14px;
            letter-spacing: 2px;
          }
        }

        &:hover {
          border-color: #40a9ff;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(64, 169, 255, 0.2);

          .ant-pagination-item-container {
            .ant-pagination-item-link-icon {
              color: #40a9ff;
            }

            .ant-pagination-item-ellipsis {
              color: #40a9ff;
            }
          }
        }
      }

      // 控制显示的页码数量，实现更好的省略效果
      .ant-pagination-item {
        &:nth-child(n+8):nth-last-child(n+8) {
          display: none;
        }
      }

      // 当页码很多时，只显示前3个、当前页前后各1个、后3个
      &.ant-pagination-simple {
        .ant-pagination-item {
          display: inline-block;
        }
      }
    }
    
    // 表格行样式保持不变
    .shallow_gray {
      background: #fff;
    }
    
    .deep_gray {
      background: #edeff3;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .customTablePagination {
    :global {
      .ant-pagination {
        flex-wrap: wrap;
        justify-content: center;
        padding: 8px;

        // 移动端显示更少的页码项
        .ant-pagination-item {
          &:nth-child(n+5):nth-last-child(n+5) {
            display: none;
          }

          min-width: 28px;
          height: 28px;
          line-height: 26px;
          font-size: 12px;
        }

        .ant-pagination-prev,
        .ant-pagination-next {
          min-width: 28px;
          height: 28px;
          line-height: 26px;
        }

        .ant-pagination-jump-prev,
        .ant-pagination-jump-next {
          min-width: 28px;
          height: 28px;
          line-height: 26px;
        }

        .ant-pagination-options {
          margin: 8px 0 0 0;
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-wrap: wrap;

          .ant-pagination-options-size-changer {
            margin: 4px;
          }

          .ant-pagination-options-quick-jumper {
            margin: 4px;

            input {
              width: 40px;
              height: 28px;
            }
          }
        }

        .ant-pagination-total-text {
          margin: 8px 0;
          width: 100%;
          text-align: center;
          font-size: 12px;
          padding: 2px 8px;
        }
      }
    }
  }
}

// 超小屏幕优化
@media (max-width: 480px) {
  .customTablePagination {
    :global {
      .ant-pagination {
        // 超小屏幕只显示当前页和前后各1页
        .ant-pagination-item {
          &:nth-child(n+4):nth-last-child(n+4) {
            display: none;
          }
        }

        .ant-pagination-options-size-changer {
          .ant-select-selector {
            font-size: 12px;
          }
        }
      }
    }
  }
}
