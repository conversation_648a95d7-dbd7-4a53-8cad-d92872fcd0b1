import React, { Component, Fragment } from 'react';
import { Form, message, Steps, Select, Input, Table, Alert, Checkbox, Row, Col, Icon } from 'antd';
import { connect, createShadow } from 'dryad';
import Button from '@/components/PAntd/PButton';
import Card from '@/components/PAntd/PCard';
import DynamicQuery from '@/components/DynamicQuery';
import StandardTable from '@/components/StandardTable';
import styles from '@/components/StandardTable/index.less';
import customStyles from './selecteResource.less';
import { backgroundColor } from 'echarts/lib/theme/dark';
import { decrypt } from '@/utils/EncryptDecryp'
import { hyposensitization, desensitizeEmail } from '@/utils/utils'
import classNames from './selecteResource.less';
const CheckboxGroup = Checkbox.Group;
const { Option } = Select;
const defaultPagination = {
  length: 10,
  start: 0,
  sord: 'desc',
  sidx: 'id',
};
@connect(({ organizationManageNew, loading }) => ({
  organizationManageNew,
}))
class SelectResource extends Component {
  state = {
    moduleId: '',
    resourceDeviceList: [],
    selectValue: '',
    fieldList: [],
    selectedRows: [],
    selectedRowsKeys: [],
    total: 0,
    needTotalList: [],
    checkedList: [],
    checkedValues: [],
    searchParams: [],
    resourceModuleList: [],
    moduleType: this.props.moduleType,
    moduleName: "",
    optionData: [],
  };
  componentDidUpdate(prevProps) {
    if (JSON.stringify(this.props.resourceList)
      !== JSON.stringify(prevProps.resourceList)
    ) {
      const { resourceList, modalList } = this.props;
      let keyMap = {
        "userId": "id",
      };

      for (let i = 0; i < resourceList.length; i++) {
        let obj = resourceList[i];
        for (let key in obj) {
          let newKey = keyMap[key];
          if (newKey) {
            obj[newKey] = obj[key];
            delete obj[key];
          }
        }
      }
      let selectedRowsKeys = [];
      if (resourceList.length > 0) {
        if (modalList === prevProps.modalList) {
          selectedRowsKeys = resourceList.map(item => item.id);
          this.setState({
            selectedRows: resourceList,
            selectedRowsKeys,
            needTotalList: [],
            checkedList: [],
            checkedValues: [],
          });
        }
        // else {
        //   this.setState({
        //     selectedRows: [],
        //     selectedRowsKeys: [],
        //     needTotalList: [],
        //     checkedList: [],
        //     checkedValues: [],
        //   });
        // }
      } else {
        this.setState({
          selectedRows: [],
          selectedRowsKeys: [],
          needTotalList: [],
          checkedList: [],
        });
      }
    }
    if (this.props.visible !== prevProps.visible) {
      // this.getModuleDeviceList();
    }
  }
  componentDidMount() {
    this.props.onRef(this)
    // this.getModuleDeviceList();
    this.getModuleDeviceList()
    this.props.dispatch({
      type: 'organizationManageNew/findUserPager',
      payload: {
        ...defaultPagination,
        username: ''
      },
      callback: response => {
        // if (response.code === 200) {
        this.setState({
          optionData: response.data,
        });
        // }
      },
    });

  }

  // shouldComponentUpdate(nextProps, nextState, nextContext) {
  //   if(
  //     &&JSON.stringify(nextProps?.resourceList)===JSON.stringify(this.props?.resourceList)) {
  //     return false
  //   }
  //   return  true
  // }

  getTableData = (name) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'organizationManageNew/findUserPager',
      payload: {
        ...defaultPagination,
        username: '' || name
      },
      callback: response => {
        // if (response.code === 200) {
        this.setState({
          resourceDeviceList: response.data,
          total: response.iTotalRecords,
        });
        // }
      },
    });
  }
  getModuleDeviceList = () => {
    const { resourceList } = this.props;
    let keyMap = {
      "userId": "id",
    };

    for (let i = 0; i < resourceList.length; i++) {
      let obj = resourceList[i];
      for (let key in obj) {
        let newKey = keyMap[key];
        if (newKey) {
          obj[newKey] = obj[key];
          delete obj[key];
        }
      }
    }
    if (resourceList.length > 0) {
      let selectedRowsKeys = resourceList.map(item => item.id);
      // if (resourceList[0]) {
      selectedRowsKeys = resourceList.map(item => item.id);
      this.setState({
        selectedRows: resourceList,
        selectedRowsKeys,
        needTotalList: [],
        checkedList: [],
        checkedValues: [],
      });
    } else {
      this.setState({
        selectedRows: [],
        selectedRowsKeys: [],
        needTotalList: [],
        checkedList: [],
      });
    }
    this.getTableData()
    // if (moduleType) {
    //   resourceModuleList.map(item => {
    //     if (item.id == moduleType) {
    //       this.setState({
    //         moduleName: item.orgName,
    //       });
    //     }
    //   });
    // }
  };
  // getSelectedModule=()
  selectedValue = (value, option) => {
    this.setState({
      moduleId: option.key,
      selectValue: value,
      fieldList: [],
      selectedRows: [],
      selectedRowsKeys: [],
      needTotalList: [],
      moduleName: value,
    });
    const { dispatch } = this.props;
    dispatch({
      type: 'monitorStrategyDetails/getStrategyModuleDeviceInfo',
      payload: {
        ...defaultPagination,
        moduleType: option.key,
        search: {},
      },
      callback: response => {
        const resourceDeviceList = response.resData.data;
        this.setState({
          resourceDeviceList,
          total: response.resData.total,
        });
      },
    });
    dispatch({
      type: 'monitorStrategyDetails/getModuleFieldInfo',
      payload: {
        moduleType: value,
      },
      callback: response => {
        const fieldList = response.resData.map(item => {
          return { key: item.fieldCode, label: item.fieldName };
        });
        this.setState({
          fieldList,
        });
      },
    });
  };
  // emment
  handleChange = value => {
    this.setState({ searchParams: value });
  };

  // 表格方法
  handleInfor = (params = {}) => {
    const { dispatch, form } = this.props;
    const { pagination = {}, tags, ...rest } = params;
    const newPagination = {
      ...defaultPagination,
      ...pagination,
      start: (pagination.current - 1) * pagination.pageSize,
      length: pagination.pageSize
    };
    const { moduleId, searchParams } = this.state;
    let search = {};
    searchParams.forEach(item => {
      search[item.key] = item.value;
    });
    search = JSON.stringify(search);
    dispatch({
      type: 'resourceClassification/findUserPager',
      payload: {
        ...newPagination,
        search,
      },
      callback: response => {
        this.setState({
          resourceDeviceList: response.data,
          total: response.iTotalRecords,
        });
      },
    });
    this.setState({ pagination: newPagination });
  };
  handleSelectRows = (rowsKeys, selectedRows) => {
    // this.props.setSelectValue(selectedRows);
    // this.setState({
    //   selectedRowsKeys: rowsKeys,
    //   selectedRows,
    //   checkedList: selectedRows,
    // });
  };
  handlePaginationTable = (pagination, filtersArg, sorter) => {
    this.handleInfor({ pagination });
  };
  cleanSelectedKeys = () => {
    this.handleRowSelectChange([], []);
  };
  handleRowSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowsKeys: [],
      selectedRows: [],
    });
  };
  selectRow = (record, selected) => {
    const { selectedRows, selectedRowsKeys } = this.state;
    if (selected) {
      selectedRows.push(record);
      selectedRowsKeys.push(record.id);
    } else {
      selectedRows.map((item, key) => {
        if (item.id == record.id) {
          selectedRows.splice(key, 1);
        }
      });
      selectedRowsKeys.map((item, key) => {
        if (item == record.id) {
          selectedRowsKeys.splice(key, 1);
        }
      });
    }

    this.setState(
      {
        selectedRows,
        selectedRowsKeys,
      },
      () => {
        this.props.setSelectValue(this.state.selectedRows);
      },
    );
  };
  selectAllRow = (selected, record, changedRows) => {
    const { selectedRowsKeys, selectedRows } = this.state;
    let newKeys = changedRows.map(item => item.id);
    if (selected) {
      this.setState(
        {
          selectedRows: [...selectedRows, ...changedRows],
          selectedRowsKeys: [...selectedRowsKeys, ...newKeys],
        },
        () => {
          this.props.setSelectValue(this.state.selectedRows);
        },
      );
    } else {
      changedRows.map((changedItem, changedkey) => {
        selectedRows.map((item, key) => {
          if (changedItem.id == item.id) {
            selectedRows.splice(key, 1);
          }
        });
        selectedRowsKeys.map((item, key) => {
          if (changedItem.id == item) {
            selectedRowsKeys.splice(key, 1);
          }
        });
      });
      this.setState(
        {
          selectedRowsKeys,
          selectedRows,
        },
        () => {
          this.props.setSelectValue(this.state.selectedRows);
        },
      );
    }
  };
  // 复选框
  handleDelete = () => {
    const { selectedRows, checkedValues, selectedRowsKeys } = this.state;
    this.setState(
      {
        checkedValues: [],
        selectedRows: selectedRows.filter(v => checkedValues.indexOf(v.id) === -1),
        selectedRowsKeys: selectedRowsKeys.filter(v => checkedValues.indexOf(v) === -1),
      },
      () => this.props.setSelectValue(this.state.selectedRows),
    );
  };
  handleChange1 = (e) => {
    this.setState({
      selectValue: e.target.value,
    })
  }
  handleSubmit = () => {
    const { selectValue } = this.state
    this.getTableData(selectValue)
  }
  cencle = () => {
    this.setState({
      selectedRowsKeys: [],
      selectedRows: []
    })
  }
  handleOpen = () => {
    const { resourceList } = this.props;
    let selectedRowsKeys = [];
    selectedRowsKeys = resourceList.map(item => item.id);
    this.setState({
      selectedRows: resourceList,
      selectedRowsKeys,
    }, () => this.props.setSelectValue(this.state.selectedRows));
  }
  render() {
    const {
      resourceDeviceList,
      fieldList,
      selectedRows,
      selectedRowsKeys,
      needTotalList,
      checkedList,
      checkedValues,
      pagination,
      total,
      searchParams,
      moduleName,
      optionData
    } = this.state;
    const { resourceModuleList } = this.props;
    const tableAlert = true;
    const rowSelection = {
      selectedRowKeys: selectedRowsKeys,
      onChange: this.handleSelectRows,
      onSelect: this.selectRow,
      onSelectAll: this.selectAllRow,
    };
    // const rowSelection = {
    //   selectedRowKeys:selectedRowsKeys,
    //   onChange: this.handleSelectRows,
    //   hideDefaultSelections: true,
    // };
    const columns = [
      { title: '用户名称', dataIndex: 'username', key: 'username' },
      {
        title: '联系方式', dataIndex: 'phonenum', key: 'phonenum', render: (text, record) => {
          return hyposensitization(decrypt(text));
        }
      },
      {
        title: '邮箱', dataIndex: 'email', key: 'email', render: (text, record) => {
          return desensitizeEmail(decrypt(text));
        }
      },
    ];

    const columns1 = [
      {
        render: record => {
          return (
            <span>
              {record.username}({hyposensitization(decrypt(record.phonenum))})
            </span>
          );
        },
        title: (
          <div
            style={{
              width: '300px',
              height: '20px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              background: '#fafafa',
            }}
          >
            <div>
              <Button
                disabled={checkedValues.length === 0}
                onClick={this.handleDelete}
                style={{ float: 'left' }}
              >
                批量移除
              </Button>
              <div
                style={{
                  fontSize: '12px',
                  fontWeight: 'normal',
                  float: 'left',
                  marginTop: '8px',
                  marginLeft: ' 20px',
                }}
              >
                已选择：{checkedValues.length}/{selectedRows.length}个
              </div>
            </div>
          </div>
        ),
        key: 'deviceName',
        width: 200,
      },
    ];
    return (
      <Card style={{ width: '100%' }}>
        <div style={{ marginTop: '15px', marginLeft: '15px' }}>
          {/*<DynamicQuery*/}
          {/*  showReset={false}*/}
          {/*  options={fieldList}*/}
          {/*  onChange={this.handleChange}*/}
          {/*  onSearch={this.handleInfor}*/}
          {/*>*/}
          <span style={{ whiteSpace: 'nowrap' }}>用户名称：</span>
          {/*<Select*/}
          {/*  maxTagTextLength={6}*/}
          {/*  style={{ width: 160, marginRight: '179px' }}*/}
          {/*  placeholder="请选择"*/}
          {/*  // onChange={(value, key) => this.selectedValue(value, key)}*/}
          {/*  onChange={(value) => this.handleChange(value)}*/}
          {/*  // disabled={searchParams.length === 0 ? false : true}*/}
          {/*  // placeholder={moduleName}*/}
          {/*  // value={moduleName}*/}
          {/*  allowClear*/}
          {/*>*/}
          {/*  {*/}
          {/*    optionData.map(item => (*/}
          {/*      <option value={item.username}>{item.username}</option>*/}
          {/*    ))*/}
          {/*  }*/}
          {/*</Select>*/}
          <Input placeholder='请输入'
            onChange={(e) => this.handleChange1(e)}
            style={{ width: 160, marginRight: '179px' }}
            allowClear
          />
          <Button type="primary" onClick={this.handleSubmit}>
            查询
          </Button>
          {/*<Select*/}
          {/*  maxTagTextLength={6}*/}
          {/*  style={{ width: 160, marginRight: '15px' }}*/}
          {/*  placeholder="请选择"*/}
          {/*  onChange={(value, key) => this.selectedValue(value, key)}*/}
          {/*  disabled={searchParams.length === 0 ? false : true}*/}
          {/*  placeholder={moduleName}*/}
          {/*  value={moduleName}*/}
          {/*>*/}
          {/*  {resourceModuleList.map(item => (*/}
          {/*    <Option maxTagTextLength={6} key={item.moduleId}>*/}
          {/*      {item.moduleName}*/}
          {/*    </Option>*/}
          {/*  ))}*/}
          {/*</Select>*/}
          {/*</DynamicQuery>*/}
        </div>
        <Row>
          <Col span={12}>
            <Card style={{ width: '100%', float: 'left' }}>
              <div className={styles.standardTable}>
                {tableAlert && (
                  <div className={styles.tableAlert}>
                    <Alert
                      message={
                        <Fragment>
                          已选择 <a style={{ fontWeight: 600 }}>{selectedRowsKeys.length}</a>{' '}
                          项&nbsp;&nbsp;
                          {/*{needTotalList.map(item => (*/}
                          {/*  <span style={{ marginLeft: 8 }} key={item.dataIndex}>*/}
                          {/*    {item.title}总计&nbsp;*/}
                          {/*    <span style={{ fontWeight: 600 }}>*/}
                          {/*      {item.render ? item.render(item.total) : item.total}*/}
                          {/*    </span>*/}
                          {/*  </span>*/}
                          {/*))}*/}
                          {
                            // <a onClick={this.cleanSelectedKeys} style={{ marginLeft: 24 }}>
                            //   清空
                            // </a>
                          }
                        </Fragment>
                      }
                      type="info"
                      showIcon
                    />
                  </div>
                )}
                <div className={classNames.customTablePagination}>
                  <Table
                    rowSelection={rowSelection}
                    dataSource={resourceDeviceList}
                    // dataSource={[]}
                    columns={columns}
                    // rowKey="id"
                    rowKey={record => record.id}
                    pagination={{
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) => `共 ${total} 条记录，当前显示 ${range[0]}-${range[1]} 条`,
                      pageSizeOptions: ['10', '20', '50', '100'],
                      showLessItems: true, // 显示较少的页码项
                      ...pagination,
                      total,
                    }}
                    scroll={{ x: '100%' }}
                    onChange={this.handlePaginationTable}
                    rowClassName={(record, index) => {
                      const className = index % 2 ? 'shallow_gray' : 'deep_gray';
                      return className
                    }}
                  />
                </div>
              </div>
            </Card>
          </Col>


          <Col span={12}>
            <Card style={{ width: '100%', marginLeft: '15px', float: 'left' }}>
              <div className={classNames.customTablePagination}>
                <Table
                  key="table1"
                  rowKey={record => record.id}
                  size="small"
                  columns={columns1}
                  dataSource={selectedRows}
                  // dataSource={[]}
                  pagination={false}
                  scroll={{ y: 380 }}
                  rowSelection={{
                    columnWidth: '25px',
                    selectedRowKeys: checkedValues,
                    onChange: selectedRowKeys => {
                      this.setState({
                        checkedValues: selectedRowKeys,
                      });
                    },
                  }}
                  rowClassName={(record, index) => {
                    const className = index % 2 ? 'shallow_gray' : 'deep_gray';
                    return className
                  }}
                />
              </div>
            </Card>
          </Col>
        </Row>
      </Card>
    );
  }
}

export default SelectResource;
